// components/product-compare-input/product-compare-input.js
Component({
  /**
   * 组件的属性列表
   */
  properties: {
    // 是否显示对比面板
    visible: {
      type: Boolean,
      value: false
    }
  },

  /**
   * 组件的初始数据
   */
  data: {
    // 对比产品列表（从全局状态获取）
    compareProducts: [],
    // 最大对比产品数量（从全局状态获取）
    maxCompareCount: 6
  },

  /**
   * 组件生命周期
   */
  lifetimes: {
    attached() {
      // 组件初始化时获取全局状态
      this.updateFromGlobalState();
      
      // 监听全局对比列表更新事件
      const app = getApp();
      app.getEventChannel().on('compareProductsUpdated', this.onCompareProductsUpdated.bind(this));
    },
    
    detached() {
      // 组件销毁时移除事件监听
      const app = getApp();
      app.getEventChannel().off('compareProductsUpdated', this.onCompareProductsUpdated.bind(this));
    }
  },

  /**
   * 组件的方法列表
   */
  methods: {
    /**
     * 从全局状态更新组件数据
     */
    updateFromGlobalState() {
      const app = getApp();
      this.setData({
        compareProducts: app.getCompareProducts(),
        maxCompareCount: app.globalData.maxCompareCount
      });
    },

    /**
     * 监听全局对比列表更新事件
     */
    onCompareProductsUpdated(data) {
      this.setData({
        compareProducts: data.compareProducts
      });
    },

    /**
     * 切换对比面板显示状态
     */
    togglePanel() {
      this.triggerEvent('toggleVisible', {
        visible: !this.properties.visible
      });
    },

    /**
     * 移除对比产品
     */
    removeProduct(e) {
      const { index } = e.currentTarget.dataset;
      const app = getApp();
      app.removeFromCompare(index);
    },

    /**
     * 清空所有对比产品
     */
    clearAll() {
      wx.showModal({
        title: '确认清空',
        content: '确定要清空所有对比产品吗？',
        success: (res) => {
          if (res.confirm) {
            const app = getApp();
            app.clearCompareProducts();
          }
        }
      });
    },

    /**
     * 开始传统对比
     */
    startCompare() {
      const app = getApp();
      app.startTraditionalCompare();
    },

    /**
     * 开始AI智能对比
     */
    startCompareV4() {
      const app = getApp();
      app.startAICompare();
    },

    /**
     * 产品点击事件
     */
    onProductTap(e) {
      const { product } = e.currentTarget.dataset;
      this.triggerEvent('productTap', {
        product: product
      });
    },

    /**
     * 图片加载错误处理
     */
    onImageError(e) {
      console.log('对比产品图片加载失败:', e);
    }
  }
});