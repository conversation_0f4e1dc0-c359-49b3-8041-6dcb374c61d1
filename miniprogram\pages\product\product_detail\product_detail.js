// pages/product/product_detail/product_detail.js
const api = require('../../../utils/api');

Page({
  /**
   * 页面的初始数据
   */
  data: {
    // 产品参数数据
    productInfo: null,
    productName: '',
    
    // 页面状态
    loading: false,
    error: null,
    
    // 展示控制
    showFullSpecs: false,
    expandedCategories: {}, // 控制各个分类的展开状态
    
    // 配置选择
    selectedConfig: null,
    showConfigModal: false,
    
    // 动态参数展示
    displaySpecs: {}, // 当前显示的规格参数（通用+配置特有）
    varyingParamKeys: [], // 不同配置间变化的参数键
    varyingCategories: [], // 包含变化参数的分类
    
    // 分享状态
    shareImagePath: '',
    
    // 产品对比相关
    compareVisible: false,
    isInCompareList: false
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    console.log('产品详情页面加载参数:', options);
    
    if (options.productName) {
      this.setData({
        productName: decodeURIComponent(options.productName)
      });
      this.loadProductParams();
    } else {
      this.setData({
        error: '缺少产品名称参数'
      });
    }
  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
    // 检查产品是否在对比列表中
    this.checkCompareStatus();
    
    // 监听全局对比状态更新
    const app = getApp();
    app.globalData.eventChannel.on('compareProductsUpdated', this.onGlobalCompareUpdated.bind(this));
  },
  
  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {
    // 移除事件监听
    const app = getApp();
    app.globalData.eventChannel.off('compareProductsUpdated', this.onGlobalCompareUpdated.bind(this));
  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {
    this.loadProductParams();
  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {
    const { productInfo } = this.data;
    
    if (productInfo) {
      return {
        title: `${productInfo.basic.skuName} - 产品详情`,
        path: `/pages/product/product_detail/product_detail?productName=${encodeURIComponent(productInfo.basic.skuName)}`,
        imageUrl: productInfo.basic.imageUrl || ''
      };
    }
    
    return {
      title: '产品详情',
      path: '/pages/product/product_detail/product_detail'
    };
  },

  /**
   * 加载产品参数
   */
  loadProductParams() {
    if (!this.data.productName) {
      this.setData({
        error: '产品名称为空'
      });
      return;
    }

    this.setData({
      loading: true,
      error: null
    });

    // 检查用户登录状态，如果已登录则传入用户ID
    let userId = null;
    const app = getApp();
    if (app.globalData.isLoggedIn && app.globalData.userInfo && app.globalData.userInfo.id) {
      userId = app.globalData.userInfo.id;
      console.log('用户已登录，传入用户ID:', userId);
    } else {
      console.log('用户未登录，不传入用户ID');
    }

    api.product.getProductParams(this.data.productName, userId)
      .then(res => {
        console.log('获取产品参数成功:', res);
        
        if (res.success) {
          const productInfo = res.data.product;
          // 添加productID和ratingStats到productInfo中
          productInfo.productID = res.data.productID;
          productInfo.ratingStats = res.data.ratingStats;
          
          // 设置导航栏标题
          wx.setNavigationBarTitle({
            title: this.truncateTitle(productInfo.basic.skuName)
          });
          
          this.setData({
            productInfo,
            selectedConfig: productInfo.configurations.defaultConfigDetails || productInfo.configurations.allConfigurations[0],
            loading: false
          });
          
          // 初始化规格分类展开状态
          this.initExpandedCategories();
          
          // 分析变化的参数
          this.analyzeVaryingParams();
          
          // 更新显示的规格参数
          this.updateDisplaySpecs();
          
          // 检查产品是否在对比列表中
          this.checkCompareStatus();
          
        } else {
          this.setData({
            error: res.message || '获取产品参数失败',
            loading: false
          });
        }
      })
      .catch(err => {
        console.error('获取产品参数失败:', err);
        this.setData({
          error: err.message || '网络错误，请重试',
          loading: false
        });
      })
      .finally(() => {
        wx.stopPullDownRefresh();
      });
  },

  /**
   * 格式化价格显示
   */
  formatPrice(price) {
    if (!price || price <= 0) {
      return '暂无价格';
    }
    return `¥${price}`;
  },

  /**
   * 格式化价格区间显示
   */
  formatPriceRange(pricing) {
    if (!pricing || !pricing.hasPrice) {
      return '暂无价格';
    }
    
    if (pricing.minPrice === pricing.maxPrice) {
      return pricing.minPrice > 0 ? `¥${pricing.minPrice}` : '暂无价格';
    }
    
    return `¥${pricing.minPrice} - ¥${pricing.maxPrice}`;
  },

  /**
   * 截取标题长度
   */
  truncateTitle(title) {
    if (!title) return '产品详情';
    return title.length > 12 ? title.substring(0, 12) + '...' : title;
  },

  /**
   * 分析变化的参数
   */
  analyzeVaryingParams() {
    const { productInfo } = this.data;
    if (!productInfo || !productInfo.configurations.allConfigurations) return;

    const varyingParamKeys = [];
    const varyingCategories = [];
    const allConfigs = productInfo.configurations.allConfigurations;
    
    // 收集所有配置的参数
    const allParams = {};
    allConfigs.forEach(config => {
      if (config.specs) {
        Object.keys(config.specs).forEach(category => {
          if (!allParams[category]) {
            allParams[category] = {};
          }
          Object.keys(config.specs[category]).forEach(param => {
            if (!allParams[category][param]) {
              allParams[category][param] = new Set();
            }
            allParams[category][param].add(config.specs[category][param]);
          });
        });
      }
    });

    // 找出变化的参数和包含变化参数的分类
    Object.keys(allParams).forEach(category => {
      let hasVaryingParam = false;
      
      Object.keys(allParams[category]).forEach(param => {
        if (allParams[category][param].size > 1) {
          varyingParamKeys.push(`${category}.${param}`);
          hasVaryingParam = true;
        }
      });
      
      // 如果该分类包含变化参数，添加到变化分类列表
      if (hasVaryingParam) {
        varyingCategories.push(category);
      }
    });

    this.setData({
      varyingParamKeys,
      varyingCategories
    });
  },

  /**
   * 更新显示的规格参数
   */
  updateDisplaySpecs() {
    const { productInfo, selectedConfig } = this.data;
    if (!productInfo || !selectedConfig) return;

    // 开始构建显示的规格参数
    const displaySpecs = {};
    
    // 1. 先添加通用规格
    if (productInfo.specifications.common) {
      Object.keys(productInfo.specifications.common).forEach(category => {
        displaySpecs[category] = { ...productInfo.specifications.common[category] };
      });
    }

    // 2. 添加/覆盖当前配置特有的规格
    if (selectedConfig.specs) {
      Object.keys(selectedConfig.specs).forEach(category => {
        if (!displaySpecs[category]) {
          displaySpecs[category] = {};
        }
        
        // 将配置特有的参数合并到对应分类中
        Object.keys(selectedConfig.specs[category]).forEach(param => {
          displaySpecs[category][param] = selectedConfig.specs[category][param];
        });
      });
    }

    this.setData({
      displaySpecs
    });
  },

  /**
   * 初始化规格分类展开状态
   */
  initExpandedCategories() {
    const { displaySpecs } = this.data;
    if (!displaySpecs) return;

    const expandedCategories = {};
    
    // 默认展开前3个分类
    const categories = Object.keys(displaySpecs);
    categories.slice(0, 3).forEach(category => {
      expandedCategories[category] = true;
    });

    this.setData({
      expandedCategories
    });
  },

  /**
   * 切换规格分类展开状态
   */
  toggleCategory(e) {
    const category = e.currentTarget.dataset.category;
    const { expandedCategories } = this.data;
    
    this.setData({
      [`expandedCategories.${category}`]: !expandedCategories[category]
    });
  },

  /**
   * 显示配置选择弹窗
   */
  showConfigSelector() {
    const { productInfo } = this.data;
    if (!productInfo || !productInfo.configurations.allConfigurations || productInfo.configurations.allConfigurations.length <= 1) {
      wx.showToast({
        title: '暂无其他配置',
        icon: 'none'
      });
      return;
    }

    this.setData({
      showConfigModal: true
    });
  },

  /**
   * 隐藏配置选择弹窗
   */
  hideConfigModal() {
    this.setData({
      showConfigModal: false
    });
  },

  /**
   * 选择配置
   */
  selectConfig(e) {
    const config = e.currentTarget.dataset.config;
    this.setData({
      selectedConfig: config,
      showConfigModal: false
    });
    
    // 更新显示的规格参数
    this.updateDisplaySpecs();
    
    // 重新初始化展开状态
    this.initExpandedCategories();
  },

  /**
   * 复制产品名称
   */
  copyProductName() {
    const { productInfo } = this.data;
    if (!productInfo) return;

    wx.setClipboardData({
      data: productInfo.basic.skuName,
      success: () => {
        wx.showToast({
          title: '产品名称已复制',
          icon: 'success'
        });
      }
    });
  },

  /**
   * 重新加载
   */
  onRetry() {
    this.loadProductParams();
  },

  /**
   * 添加/移除产品对比
   */
  addToCompare() {
    const { productInfo, isInCompareList } = this.data;
    if (!productInfo) return;

    const app = getApp();
    
    if (isInCompareList) {
      // 从对比列表中移除
      const compareProducts = app.getCompareProducts();
      const index = compareProducts.findIndex(p => p.skuId === productInfo.basic.skuId);
      if (index !== -1) {
        app.removeFromCompare(index);
      }
    } else {
      // 添加到对比列表
      const product = {
        skuId: productInfo.basic.skuId,
        skuName: productInfo.basic.skuName,
        brandName: productInfo.basic.brandName,
        productType: productInfo.basic.productType,
        imageUrl: productInfo.basic.imageUrl
      };
      app.addToCompare(product);
    }
  },
  
  /**
   * 检查产品是否在对比列表中
   */
  checkCompareStatus() {
    const { productInfo } = this.data;
    if (!productInfo) return;
    
    const app = getApp();
    const isInCompareList = app.isInCompare(productInfo.basic.skuId);
    
    this.setData({
      isInCompareList
    });
  },
  
  /**
   * 切换对比面板显示状态
   */
  onToggleCompareVisible() {
    this.setData({
      compareVisible: !this.data.compareVisible
    });
  },
  
  /**
   * 全局对比状态更新回调
   */
  onGlobalCompareUpdated() {
    this.checkCompareStatus();
  },

  /**
   * 查看图片
   */
  previewImage() {
    const { productInfo } = this.data;
    if (!productInfo || !productInfo.basic.imageUrl) return;

    wx.previewImage({
      urls: [productInfo.basic.imageUrl],
      current: productInfo.basic.imageUrl
    });
  },

  /**
   * 显示价格说明
   */
  showPriceInfo() {
    wx.showModal({
      title: '价格说明',
      content: '价格数据来源于网络，仅供参考，实际价格以商家为准。不同配置价格可能有所差异。',
      showCancel: false,
      confirmText: '知道了'
    });
  },

  /**
   * 处理评分提交事件
   */
  async onSubmitRating(e) {
    const { targetId, ratingType, rating, onSuccess, onError } = e.detail;
    console.log('处理评分提交:', { targetId, ratingType, rating });

    try {
      // 根据评分类型调用不同的API
      let result;
      if (ratingType === 'product') {
        result = await api.user.rateProduct(targetId, rating);
      } else {
        throw new Error(`不支持的评分类型: ${ratingType}`);
      }

      if (result.success) {
        // 调用组件的成功回调
        onSuccess(result.data);
      } else {
        throw new Error(result.message || '评分失败');
      }
    } catch (error) {
      console.error('评分提交失败:', error);
      // 调用组件的错误回调
      onError(error);
    }
  },

  /**
   * 处理评分更新事件
   */
  onRatingUpdated(e) {
    const { averageRating, totalRatings, userRating } = e.detail;
    console.log('评分更新:', { averageRating, totalRatings, userRating });

    // 更新页面数据中的评分统计
    this.setData({
      'productInfo.ratingStats.averageRating': averageRating,
      'productInfo.ratingStats.totalRatings': totalRatings
    });

    // 显示评分成功提示
    wx.showToast({
      title: `评分成功: ${userRating}分`,
      icon: 'success',
      duration: 2000
    });
  }
});
